name: 🐛 Bugs
description: Report bugs
labels: ["bug"]
title: "Please read & provide the following"
body:
  - type: markdown
    attributes:
      value: |
        ## Instructions To Reproduce the 🐛 Bug:

        1. Background explanation

  - type: textarea
    attributes:
      label: "Full runnable code or full changes you made:"
      description: Please provide the code or changes that led to the bug.
      placeholder: |
        ```
        ```
    validations:
      required: true

  - type: textarea
    attributes:
      label: "What exact command you ran:"
      description: Describe the exact command you ran that triggered the bug.
    validations:
      required: true

  - type: markdown
    attributes:
      value: |
        1. Please simplify the steps as much as possible so they do not require additional resources to
        run, such as a private dataset.

        ## Expected behavior:

        If there are no obvious errors in "full logs" provided above,
        please tell us the expected behavior.

  - type: textarea
    attributes:
      label: Expected behavior
      description: Describe the expected behavior if the bug had not occurred.
    validations:
      required: true

  - type: checkboxes
    attributes:
      label: Environment
      description: Indicate your environment details.
      options:
        - label: "I'm using the latest version!"
        - label: "It's not a user-side mistake!"
