MinkowskiNormalization
======================


MinkowskiBatchNorm
------------------

.. autoclass:: MinkowskiEngine.MinkowskiBatchNorm
    :members: cpu, cuda, double, float, to, type, forward
    :undoc-members:
    :exclude-members:

    .. automethod:: __init__


MinkowskiSyncBatchNorm
----------------------

.. autoclass:: MinkowskiEngine.MinkowskiSyncBatchNorm
    :members: cpu, cuda, double, float, to, type, forward
    :undoc-members:
    :exclude-members:

    .. automethod:: __init__



MinkowskiInstanceNorm
---------------------

.. autoclass:: MinkowskiEngine.MinkowskiInstanceNorm
    :members: cpu, cuda, double, float, to, type, forward
    :undoc-members:
    :exclude-members:

    .. automethod:: __init__
