Welcome to MinkowskiEngine's documentation!
===========================================

The MinkowskiEngine is an auto-differentiation library for sparse tensors.

Table of Contents
=================

.. toctree::
   :maxdepth: 1
   :caption: Introduction

   overview
   sparse_tensor_network
   quick_start
   terminology

.. toctree::
   :maxdepth: 2
   :caption: Tutorials

   tutorial/sparse_tensor_basic

.. toctree::
   :maxdepth: 2
   :caption: Demos

   demo/training
   demo/modelnet40_classification
   demo/segmentation
   demo/sparse_tensor_reconstruction
   demo/interop
   demo/multigpu
   demo/pointnet

.. toctree::
   :maxdepth: 2
   :caption: API

   sparse_tensor
   convolution
   pooling
   broadcast
   normalization
   nonlinearity
   pruning
   interp
   union
   coords
   utils
   common
   misc

.. toctree::
   :maxdepth: 1
   :caption: Miscellanea

   issues
   guides
   migration_05
   benchmark


Indices and tables
==================

* :ref:`genindex`
* :ref:`search`
