SparseT<PERSON>or and <PERSON><PERSON><PERSON>ield
============================

SparseTensor
------------

.. autoclass:: MinkowskiEngine.MinkowskiSparseTensor.SparseTensor
    :members:
    :undoc-members:

    .. automethod:: __init__


TensorField
-----------

.. autoclass:: MinkowskiEngine.MinkowskiTensorField.TensorField
    :members:
    :undoc-members:

    .. automethod:: __init__


SparseTensorOperationMode
-------------------------

.. autoclass:: MinkowskiEngine.MinkowskiTensor.SparseTensorOperationMode
    :members:

SparseTensorQuantizationMode
----------------------------

.. autoclass:: MinkowskiEngine.MinkowskiTensor.SparseTensorQuantizationMode
    :members:

set_sparse_tensor_operation_mode
--------------------------------

.. autofunction:: MinkowskiEngine.MinkowskiTensor.set_sparse_tensor_operation_mode

sparse_tensor_operation_mode
----------------------------

.. autofunction:: MinkowskiEngine.MinkowskiTensor.sparse_tensor_operation_mode

global_coordinate_manager
-------------------------

.. autofunction:: MinkowskiEngine.MinkowskiTensor.global_coordinate_manager

set_global_coordinate_manager
-----------------------------

.. autofunction:: MinkowskiEngine.MinkowskiTensor.set_global_coordinate_manager

clear_global_coordinate_manager
-------------------------------

.. autofunction:: MinkowskiEngine.MinkowskiTensor.clear_global_coordinate_manager
