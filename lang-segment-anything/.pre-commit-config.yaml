
default_language_version:
  python: python3.11


repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.1.0
    hooks:
      - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: check-yaml
      - id: check-docstring-first
      - id: check-executables-have-shebangs
      - id: check-case-conflict
      - id: check-added-large-files
        args: ['--maxkb=500', '--enforce-all']
      - id: detect-private-key

  - repo: https://github.com/commitizen-tools/commitizen
    rev: v2.27.0
    hooks:
      - id: commitizen
        stages: [commit-msg]
        name: Format commit msg

  - repo: https://github.com/asottile/pyupgrade
    rev: v2.31.0
    hooks:
      - id: pyupgrade
        args: [--py311-plus]
        name: Upgrade code

  - repo: https://github.com/myint/docformatter
    rev: 06907d0267368b49b9180eed423fae5697c1e909 # todo: fix for docformatter after last 1.7.5
    hooks:
      - id: docformatter
        args: [--in-place, --wrap-summaries=115, --wrap-descriptions=120]

  - repo: https://github.com/asottile/yesqa
    rev: v1.3.0
    hooks:
      - id: yesqa
        name: Unused noqa

  - repo: https://github.com/charliermarsh/ruff-pre-commit
    # Ruff version.
    rev: 'v0.0.235'
    hooks:
      - id: ruff

  - repo: https://github.com/asottile/blacken-docs
    rev: v1.12.0
    hooks:
      - id: blacken-docs
        args: [--line-length=120]
        additional_dependencies: [black==21.12b0]

  - repo: https://github.com/executablebooks/mdformat
    rev: 0.7.11
    hooks:
      - id: mdformat
        additional_dependencies:
          - mdformat-gfm
          - mdformat_frontmatter
        exclude: CHANGELOG.md
