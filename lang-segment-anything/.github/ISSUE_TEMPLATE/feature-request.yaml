name: 🚀 Feature Request
description: Suggest an improvement or new feature
labels: ["enhancement"]
body:
  - type: markdown
    attributes:
      value: |
        ## 🚀 Feature

        A clear and concise description of the feature proposal.

  - type: textarea
    attributes:
      label: Motivation & Examples
      description: |
        Tell us why the feature is useful.

        Describe what the feature would look like if it is implemented. Best demonstrated using **code examples** in addition to words.
      placeholder: |
        <put sample here>
    validations:
      required: true

  - type: markdown
    attributes:
      value: |
        ## Note

        We only consider adding new features if they are relevant to this library.
        Consider if this new feature deserves to be here or should be a new library.
